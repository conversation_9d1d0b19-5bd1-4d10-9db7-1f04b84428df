import polars as pl
import json
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from itertools import combinations
import os
import pickle

periods = [("0930","1129"), ("1300","1459")]

VALID_TIME = ["0925"] + [
    f"{m//60:02d}{m%60:02d}"
    for start, end in periods
    for m in range(
        int(start[:2])*60 + int(start[2:]),
        int(end[:2])*60   + int(end[2:]) + 1
    )
] 

SPEC_TIME = ["0959", "1029", "1059", "1129", "1329", "1359", "1429"]

base_dir = "/disk4/shared/intern/laiyc/alpha"
factor_list = [i.split(".")[0] for i in os.listdir(f"{base_dir}")]