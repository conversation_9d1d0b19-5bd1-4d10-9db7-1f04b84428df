import polars as pl
import numpy as np
from typing import Sequence

FACTOR_COLS: Sequence[str] = ['Open', 'High', 'Low', 'Close', 'Vwap', 'Volume', 'Amount']

def build_np_cube_from_parquet(
    main_parquet: str,
    hhmm_map_parquet: str,   # 包含列: 'hhmm' -> 'MinuteId' (0..240)
    sym_map_parquet: str,    # 包含列: 'symbol' -> 'SymbolId' (0..5999)
    factor_cols: Sequence[str] = FACTOR_COLS,
    date_col: str = "date",
    hhmm_col: str = "hhmm",
    symbol_col: str = "symbol",
    minute_id_col: str = "MinuteId",
    symbol_id_col: str = "SymbolId",
    dtype_float = pl.Float32,
):
    # 1) 基表（LazyFrame），只取必要列
    base = (
        pl.scan_parquet(main_parquet)
          .select([date_col, hhmm_col, symbol_col, *factor_cols])
    )

    # 2) 映射到 MinuteId / SymbolId
    hhmm_map = pl.scan_parquet(hhmm_map_parquet)  # ['hhmm','MinuteId']
    sym_map  = pl.scan_parquet(sym_map_parquet)   # ['symbol','SymbolId']

    mapped = (
        base.join(hhmm_map, on=hhmm_col, how="left")
            .join(sym_map,  on=symbol_col, how="left")
            .with_columns(
                pl.col(minute_id_col).cast(pl.UInt16),
                pl.col(symbol_id_col).cast(pl.UInt16),
            )
    )

    # 3) 日期 → d_idx（升序）
    date_series = (
        mapped.select(date_col).unique().collect()
               .sort(date_col)[date_col]                 # Series
    )
    D = len(date_series)
    date_map = pl.DataFrame({
        date_col: date_series,
        "d_idx":  np.arange(D, dtype=np.uint32)
    })
    # 注意：把 DataFrame 变为 Lazy 用于 join
    mapped = mapped.join(date_map.lazy(), on=date_col, how="left")

    # 4) 选取索引 + 值列（一次 collect，保证对齐）
    select_cols = ["d_idx", minute_id_col, symbol_id_col, *factor_cols]
    tbl = (
        mapped.select(select_cols)
              .collect(streaming=True)  # 关键：一次收集，索引和值列一一对齐
    )

    # 5) 转 Numpy
    d_idx  = tbl["d_idx"        ].to_numpy()      # uint32
    m_idx  = tbl[minute_id_col  ].to_numpy()      # uint16
    s_idx  = tbl[symbol_id_col  ].to_numpy()      # uint16

    # 维度信息
    F = len(factor_cols)
    H = 241
    S = 6000

    # 6) 预分配结果张量，并矢量化填充
    cube = np.full((F, D, H, S), np.nan, dtype=np.float32)

    # 可选：若存在重复 (d_idx, m_idx, s_idx)，可先做聚合（如 last/mean）：
    # tbl = tbl.group_by(["d_idx", minute_id_col, symbol_id_col]).agg([pl.col(c).last() for c in factor_cols]).collect(streaming=True)
    # 然后再取 numpy 列

    # 简单校验索引范围
    if not (np.nanmax(m_idx) < H and np.nanmax(s_idx) < S):
        raise ValueError("MinuteId 或 SymbolId 超出范围；请检查映射。")

    # 分块 scatter，避免一次创建过大的临时数组
    n = len(d_idx)
    chunk = 5_000_000  # 可调
    for start in range(0, n, chunk):
        end = min(start + chunk, n)
        dd = d_idx[start:end]
        mm = m_idx[start:end]
        ss = s_idx[start:end]
        for f, col in enumerate(factor_cols):
            vv = tbl[col][start:end].cast(dtype_float).to_numpy()
            cube[f, dd, mm, ss] = vv  # 高级索引赋值（原地无回拷）

    return cube, np.asarray(date_series.to_list())  # 返回日期顺序以便后续对齐
