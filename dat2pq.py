import qi.cpp.utils as cu
import qi.common_tools.np_data_loader as ndl
import os
import pandas as pd
import time
import gc
import multiprocessing as mp
from pathlib import Path
from qi.common_tools.calendar_utils import ashare_cal

# 定义处理单个文件的函数
def process_file(base_data):
    print("----- Start Time: {}, base data: {} -----".format(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), base_data))
    symbol = base_data[:-4]  # 去掉.dat后缀
    
    # 按年份处理数据
    years = range(2015, 2025+1)  # 2015到2024年
    
    for year in years:
        # 获取每年第一个交易日和最后一个交易日
        year_start = f"{year}0101"
        if year == 2025:
            year_end = "20250417"
        else:
            year_end = f"{year}1231"
        
        # 确保起始日期是交易日，如果不是则获取之后的第一个交易日
        if not ashare_cal.is_trading_day(year_start):
            year_from_date = [y for y in ashare_cal.get_trading_days_from_to(year_start, 2) if y.startswith(f"{year}")][0]
        else:
            year_from_date = year_start
            
        # 确保结束日期是交易日，如果不是则获取之前的最后一个交易日
        if not ashare_cal.is_trading_day(year_end):
            year_to_date = ashare_cal.get_trading_day(year_end, 0)
        else:
            year_to_date = year_end
        
        print(f"处理 {symbol} 年份 {year}，交易日范围: {year_from_date} 到 {year_to_date}")
        
        try:
            # 加载特定年份的数据
            np_base_data = ndl.load_minutely_dataset_by_file(
                minutely_dir + base_data, year_from_date, year_to_date, 
                ignore_miss_dates=True,  # 允许忽略缺失日期
                ignore_miss_files=True,  # 允许忽略缺失文件
            )
            
            # 如果数据为空或形状不对，跳过
            if np_base_data is None or np_base_data.size == 0:
                print(f"{symbol} 在 {year} 年没有数据，跳过。")
                continue
                
            # print(f"{symbol} {year} 数据形状: {np_base_data.shape}")
            
            # 转换为DataFrame
            df_minutely = ndl.cube_to_df(
                np_base_data,
                ndl.get_di_from_dt(year_from_date),
                ndl.get_di_from_dt(year_to_date),
                col_=symbol,
                drop_na_=False,
            )
            
            # 添加year列用于分区
            df_minutely['year'] = year
            
            # 创建目标目录
            output_dir = Path(f"/disk4/shared/intern/laiyc/minRawData/{symbol}.parquet")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建年份子目录
            year_dir = output_dir / f"year={year}"
            year_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存到分区parquet文件
            df_minutely.to_parquet(
                f"{year_dir}/data.parquet",
                index=False
            )
            
            print(f"已保存 {symbol} {year}年数据")
            
            # 清理内存
            del np_base_data, df_minutely
            gc.collect()
            
        except Exception as e:
            print(f"处理 {symbol} {year}年数据时出错: {e}")
    
    print("----- End Time: {}, base data: {} -----".format(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), base_data))
    return base_data

# 主程序部分
if __name__ == "__main__":
    minutely_dir = "/disk3/shared/wangks/Minutely/"  
    #base_data_list = [i for i in os.listdir(minutely_dir) if i.endswith(".dat")]
    base_data_list = [i for i in os.listdir(minutely_dir) \
                      if i.endswith(".dat")\
                        and i not in [f'{j}.dat' for j in \
                         ['Close', 'Open', 'High', 'Low', 'Volume', 'Amount', 'Vwap']]]
    ru_min1_list = [i for i in os.listdir(minutely_dir) if i.endswith(".dat")]
    base_data_list = base_data_list
    
    print(f"找到 {len(base_data_list)} 个数据文件")

    print(base_data_list)

    
    # 创建进程池，使用10个进程
    pool = mp.Pool(processes=18)
    
    # 使用进程池并行处理文件
    results = pool.map(process_file, base_data_list)
    
    # 关闭进程池
    pool.close()
    pool.join()
    
    print("所有文件处理完成")