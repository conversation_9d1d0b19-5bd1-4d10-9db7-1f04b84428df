import polars as pl
import qi.common_tools.np_data_loader as ndl
import numpy as np
from concurrent.futures import ProcessPoolExecutor
import os

periods = [("0930","1129"), ("1300","1459")]
VALID_TIME = ["0925"] + [
    f"{m//60:02d}{m%60:02d}"
    for start, end in periods
    for m in range(
        int(start[:2])*60 + int(start[2:]),
        int(end[:2])*60   + int(end[2:]) + 1
    )
] 

ALPHA_PATH = '/disk4/shared/intern/laiyc/alpha/'
SAVE_PATH_f32 = '/disk4/shared/intern/laiyc/alpha_dat/f32/'
SAVE_PATH_f64 = '/disk4/shared/intern/laiyc/alpha_dat/f64/'

factor_name_list = [i.split('.')[0] for i in os.listdir(ALPHA_PATH)]

hhmm_map = "./diskBig/hhmm_mapping.parquet"
sym_map  = "./diskBig/symbol_mapping.parquet"


def process_factor(factor_name: str):
    dsh = pl.scan_parquet("./diskBig/dsh.parquet")
    factor = pl.scan_parquet(ALPHA_PATH + f"{factor_name}.parquet").with_columns(
        pl.col(f"{factor_name}").cast(pl.Float32)
    )
    df = pl.concat([dsh, factor], how="horizontal")

    factor_df = (
        df
        .join(pl.scan_parquet(hhmm_map), on="hhmm",  how="left")
        .join(pl.scan_parquet(sym_map),  on="symbol", how="left")
        .with_columns(pl.col("MinuteId").cast(pl.UInt16),
                      pl.col("SymbolId").cast(pl.UInt16))
    )

    # —— 日期映射 —— 
    date_series = (factor_df.select("date").unique().collect()
                                .sort("date")["date"])
    date_map = pl.DataFrame({
        "date": date_series,
        "d_idx": np.arange(len(date_series), dtype=np.uint32)
    })

    factor_idx = (
        factor_df.join(date_map.lazy(), on="date", how="left")
                 .select("d_idx", "MinuteId", "SymbolId", f"{factor_name}")
                 .collect()
    )

    D, H, S = len(date_series), 283, 6000
    tensor = np.full((D, H, S), np.nan, dtype=np.float32)

    d_idx     = factor_idx["d_idx"].to_numpy()
    MinuteId  = factor_idx["MinuteId"].to_numpy()
    SymbolId  = factor_idx["SymbolId"].to_numpy()
    values    = factor_idx[f"{factor_name}"].to_numpy()

    tensor[d_idx, MinuteId, SymbolId] = values

    # 存 f32
    ndl.np_store_cube_customed_size_f32(
        dataset_file=SAVE_PATH_f32 + f"{factor_name}_f32.dat",
        np_data=tensor,
        size_shape_0=ndl.cpp_loader.DateSize,
        size_shape_1=283,
        size_shape_2=6000,
        shape_0_first_id=ndl.get_di_from_dt("20150105"),
        shape_0_last_id=ndl.get_di_from_dt("20250417"),
    )
    # 存 f64
    ndl.np_store_cube_customed_size(
        dataset_file=SAVE_PATH_f64 + f"{factor_name}_f64.dat",
        np_data=tensor,
        size_shape_0=ndl.cpp_loader.DateSize,
        size_shape_1=283,
        size_shape_2=6000,
        shape_0_first_id=ndl.get_di_from_dt("20150105"),
        shape_0_last_id=ndl.get_di_from_dt("20250417"),
    )

    return f"{factor_name} finished"


if __name__ == "__main__":
    with ProcessPoolExecutor(max_workers=6) as executor:
        for result in executor.map(process_factor, factor_name_list):
            print(result)