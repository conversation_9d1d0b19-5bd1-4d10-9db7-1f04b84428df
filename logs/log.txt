2025-08-04 16:13:41,337 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-04 16:13:41,623 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:13:41,624 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:13:41,624 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-04 16:13:41,624 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-04 16:13:41,624 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-04 16:13:41,649 - [constants.py:28] - INFO - =======================================
2025-08-04 16:13:41,650 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-04 16:13:41,650 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:13:41,650 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:13:41,650 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-04 16:13:41,650 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-04 16:13:41,650 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-04 16:13:41,650 - [constants.py:35] - INFO - =======================================
2025-08-04 16:13:41,699 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-04 16:13:41,699 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-04 16:13:41,714 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-04 16:13:41,779 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-04 16:13:41,779 - [__init__.py:19] - INFO - DateSize=9000
2025-08-04 16:13:41,779 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-04 16:13:41,779 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-04 16:13:41,779 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-04 16:13:41,779 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-04 16:22:48,892 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-04 16:22:49,053 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:22:49,053 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:22:49,053 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-04 16:22:49,053 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-04 16:22:49,053 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-04 16:22:49,068 - [constants.py:28] - INFO - =======================================
2025-08-04 16:22:49,068 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-04 16:22:49,068 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:22:49,068 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:22:49,068 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-04 16:22:49,068 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-04 16:22:49,068 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-04 16:22:49,068 - [constants.py:35] - INFO - =======================================
2025-08-04 16:22:49,100 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-04 16:22:49,100 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-04 16:22:49,114 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-04 16:22:49,154 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-04 16:22:49,154 - [__init__.py:19] - INFO - DateSize=9000
2025-08-04 16:22:49,154 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-04 16:22:49,154 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-04 16:22:49,154 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-04 16:22:49,155 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-04 16:23:43,415 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-04 16:23:43,578 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:23:43,578 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:23:43,578 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-04 16:23:43,578 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-04 16:23:43,578 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-04 16:23:43,593 - [constants.py:28] - INFO - =======================================
2025-08-04 16:23:43,593 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-04 16:23:43,593 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:23:43,593 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:23:43,593 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-04 16:23:43,593 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-04 16:23:43,593 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-04 16:23:43,594 - [constants.py:35] - INFO - =======================================
2025-08-04 16:23:43,624 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-04 16:23:43,624 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-04 16:23:43,643 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-04 16:23:43,683 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-04 16:23:43,683 - [__init__.py:19] - INFO - DateSize=9000
2025-08-04 16:23:43,683 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-04 16:23:43,683 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-04 16:23:43,683 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-04 16:23:43,683 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-04 16:24:24,095 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-04 16:24:24,346 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:24:24,346 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:24:24,347 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-04 16:24:24,347 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-04 16:24:24,347 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-04 16:24:24,372 - [constants.py:28] - INFO - =======================================
2025-08-04 16:24:24,372 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-04 16:24:24,372 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:24:24,372 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:24:24,372 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-04 16:24:24,372 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-04 16:24:24,372 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-04 16:24:24,372 - [constants.py:35] - INFO - =======================================
2025-08-04 16:24:24,423 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-04 16:24:24,423 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-04 16:24:24,444 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-04 16:24:24,509 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-04 16:24:24,509 - [__init__.py:19] - INFO - DateSize=9000
2025-08-04 16:24:24,509 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-04 16:24:24,509 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-04 16:24:24,509 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-04 16:24:24,509 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-04 16:24:35,884 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-04 16:24:36,047 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:24:36,048 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:24:36,048 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-04 16:24:36,048 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-04 16:24:36,048 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-04 16:24:36,063 - [constants.py:28] - INFO - =======================================
2025-08-04 16:24:36,063 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-04 16:24:36,063 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:24:36,063 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:24:36,063 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-04 16:24:36,063 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-04 16:24:36,063 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-04 16:24:36,064 - [constants.py:35] - INFO - =======================================
2025-08-04 16:24:36,094 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-04 16:24:36,094 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-04 16:24:36,109 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-04 16:24:36,149 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-04 16:24:36,149 - [__init__.py:19] - INFO - DateSize=9000
2025-08-04 16:24:36,149 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-04 16:24:36,149 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-04 16:24:36,149 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-04 16:24:36,149 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-04 16:25:36,657 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-04 16:25:36,819 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:25:36,819 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:25:36,819 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-04 16:25:36,819 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-04 16:25:36,819 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-04 16:25:36,834 - [constants.py:28] - INFO - =======================================
2025-08-04 16:25:36,834 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-04 16:25:36,834 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:25:36,834 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:25:36,834 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-04 16:25:36,834 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-04 16:25:36,834 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-04 16:25:36,834 - [constants.py:35] - INFO - =======================================
2025-08-04 16:25:36,866 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-04 16:25:36,866 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-04 16:25:36,876 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-04 16:25:36,918 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-04 16:25:36,918 - [__init__.py:19] - INFO - DateSize=9000
2025-08-04 16:25:36,918 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-04 16:25:36,918 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-04 16:25:36,918 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-04 16:25:36,918 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-04 16:26:20,645 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-04 16:26:20,809 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:26:20,809 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:26:20,809 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-04 16:26:20,809 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-04 16:26:20,809 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-04 16:26:20,825 - [constants.py:28] - INFO - =======================================
2025-08-04 16:26:20,825 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-04 16:26:20,825 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:26:20,825 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-04 16:26:20,825 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-04 16:26:20,825 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-04 16:26:20,825 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-04 16:26:20,825 - [constants.py:35] - INFO - =======================================
2025-08-04 16:26:20,855 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-04 16:26:20,855 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-04 16:26:20,866 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-04 16:26:20,906 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-04 16:26:20,906 - [__init__.py:19] - INFO - DateSize=9000
2025-08-04 16:26:20,906 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-04 16:26:20,906 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-04 16:26:20,906 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-04 16:26:20,906 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-05 09:57:49,280 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-05 09:57:49,762 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 09:57:49,763 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 09:57:49,764 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-05 09:57:49,764 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-05 09:57:49,764 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-05 09:57:49,862 - [constants.py:28] - INFO - =======================================
2025-08-05 09:57:49,862 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-05 09:57:49,862 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-05 09:57:49,862 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-05 09:57:49,863 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-05 09:57:49,863 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-05 09:57:49,863 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-05 09:57:49,863 - [constants.py:35] - INFO - =======================================
2025-08-05 09:57:49,958 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-05 09:57:49,958 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-05 09:57:49,976 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-05 09:57:50,066 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-05 09:57:50,066 - [__init__.py:19] - INFO - DateSize=9000
2025-08-05 09:57:50,066 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-05 09:57:50,066 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-05 09:57:50,066 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-05 09:57:50,066 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-05 10:02:48,113 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-05 10:02:48,276 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:02:48,276 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:02:48,276 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-05 10:02:48,276 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-05 10:02:48,276 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-05 10:02:48,292 - [constants.py:28] - INFO - =======================================
2025-08-05 10:02:48,292 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-05 10:02:48,292 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:02:48,292 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:02:48,292 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-05 10:02:48,292 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-05 10:02:48,292 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-05 10:02:48,292 - [constants.py:35] - INFO - =======================================
2025-08-05 10:02:48,322 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-05 10:02:48,322 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-05 10:02:48,331 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-05 10:02:48,367 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-05 10:02:48,367 - [__init__.py:19] - INFO - DateSize=9000
2025-08-05 10:02:48,367 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-05 10:02:48,367 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-05 10:02:48,367 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-05 10:02:48,367 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-05 10:03:27,736 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-05 10:03:27,897 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:03:27,897 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:03:27,897 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-05 10:03:27,897 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-05 10:03:27,897 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-05 10:03:27,912 - [constants.py:28] - INFO - =======================================
2025-08-05 10:03:27,912 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-05 10:03:27,912 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:03:27,912 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:03:27,912 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-05 10:03:27,912 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-05 10:03:27,912 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-05 10:03:27,912 - [constants.py:35] - INFO - =======================================
2025-08-05 10:03:27,943 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-05 10:03:27,943 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-05 10:03:27,953 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-05 10:03:27,990 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-05 10:03:27,990 - [__init__.py:19] - INFO - DateSize=9000
2025-08-05 10:03:27,990 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-05 10:03:27,990 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-05 10:03:27,990 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-05 10:03:27,990 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-05 10:07:03,391 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-05 10:07:03,552 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:07:03,552 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:07:03,553 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-05 10:07:03,553 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-05 10:07:03,553 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-05 10:07:03,568 - [constants.py:28] - INFO - =======================================
2025-08-05 10:07:03,568 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-05 10:07:03,568 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:07:03,568 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:07:03,568 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-05 10:07:03,568 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-05 10:07:03,568 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-05 10:07:03,568 - [constants.py:35] - INFO - =======================================
2025-08-05 10:07:03,598 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-05 10:07:03,598 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-05 10:07:03,606 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-05 10:07:03,643 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-05 10:07:03,643 - [__init__.py:19] - INFO - DateSize=9000
2025-08-05 10:07:03,643 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-05 10:07:03,643 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-05 10:07:03,643 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-05 10:07:03,643 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-05 10:11:19,834 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-05 10:11:20,086 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:11:20,087 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:11:20,087 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-05 10:11:20,087 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-05 10:11:20,087 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-05 10:11:20,112 - [constants.py:28] - INFO - =======================================
2025-08-05 10:11:20,112 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-05 10:11:20,112 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:11:20,112 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:11:20,112 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-05 10:11:20,112 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-05 10:11:20,112 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-05 10:11:20,112 - [constants.py:35] - INFO - =======================================
2025-08-05 10:11:20,162 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-05 10:11:20,163 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-05 10:11:20,179 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-05 10:11:20,237 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-05 10:11:20,237 - [__init__.py:19] - INFO - DateSize=9000
2025-08-05 10:11:20,237 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-05 10:11:20,237 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-05 10:11:20,237 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-05 10:11:20,237 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-05 10:17:04,692 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-05 10:17:04,853 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:17:04,854 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:17:04,854 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-05 10:17:04,854 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-05 10:17:04,854 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-05 10:17:04,869 - [constants.py:28] - INFO - =======================================
2025-08-05 10:17:04,869 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-05 10:17:04,869 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:17:04,869 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:17:04,869 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-05 10:17:04,869 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-05 10:17:04,869 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-05 10:17:04,869 - [constants.py:35] - INFO - =======================================
2025-08-05 10:17:04,899 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-05 10:17:04,899 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-05 10:17:04,912 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-05 10:17:04,949 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-05 10:17:04,949 - [__init__.py:19] - INFO - DateSize=9000
2025-08-05 10:17:04,949 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-05 10:17:04,949 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-05 10:17:04,949 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-05 10:17:04,949 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-05 10:18:01,088 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-05 10:18:01,252 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:18:01,253 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:18:01,253 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-05 10:18:01,253 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-05 10:18:01,253 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-05 10:18:01,268 - [constants.py:28] - INFO - =======================================
2025-08-05 10:18:01,268 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-05 10:18:01,268 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:18:01,268 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:18:01,268 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-05 10:18:01,268 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-05 10:18:01,268 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-05 10:18:01,268 - [constants.py:35] - INFO - =======================================
2025-08-05 10:18:01,298 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-05 10:18:01,298 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-05 10:18:01,307 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-05 10:18:01,344 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-05 10:18:01,344 - [__init__.py:19] - INFO - DateSize=9000
2025-08-05 10:18:01,344 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-05 10:18:01,344 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-05 10:18:01,344 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-05 10:18:01,344 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
2025-08-05 10:27:04,439 - [calendar_utils.py:40] - INFO - 读取[环境变量] QI_CONF_PATH 失败, 使用 default_value: /disk_nas/QDataStore_v2/qi_conf
2025-08-05 10:27:04,601 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_DATA_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:27:04,601 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LOCAL_BT_HOME->/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:27:04,602 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME->$FACTOR_PATH/Dataset_Slice_Live
2025-08-05 10:27:04,602 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: BT_SLICE_HOME->$FACTOR_PATH/Dataset_Slice_Backtest
2025-08-05 10:27:04,602 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: LIVE_DATA_HOME_V2->$NAS49_SSD1/Dataset_Slice_Live
2025-08-05 10:27:04,616 - [constants.py:28] - INFO - =======================================
2025-08-05 10:27:04,616 - [constants.py:29] - INFO - qi.common_tools.np_data_loader初始化成功:
2025-08-05 10:27:04,616 - [constants.py:30] - INFO - LOCAL_BT_HOME =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:27:04,616 - [constants.py:31] - INFO - BT_DATA_HOME  =/disk_nas/QDataStore_v2/Dataset
2025-08-05 10:27:04,616 - [constants.py:32] - INFO - LIVE_DATA_HOME=/home/<USER>/synology/factor/Dataset_Slice_Live
2025-08-05 10:27:04,617 - [constants.py:33] - INFO - LIVE_DATA_HOME_V2=/home/<USER>/synology/49_ssd_nfs1/Dataset_Slice_Live
2025-08-05 10:27:04,617 - [constants.py:34] - INFO - BT_SLICE_HOME =/home/<USER>/synology/factor/Dataset_Slice_Backtest
2025-08-05 10:27:04,617 - [constants.py:35] - INFO - =======================================
2025-08-05 10:27:04,647 - [template_utils.py:115] - WARNING - 读取环境变量不存在, 返回default_value: DSM_FILE->/disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt
2025-08-05 10:27:04,647 - [logger.py:29] - INFO - loading v2 pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt...
2025-08-05 10:27:04,658 - [logger.py:29] - INFO - loading pqt from file /disk_nas/QDataStore_v2/Dataset/../qi_conf/v2_dsm/dataset_conf.pqt done, shape=(0, 67), st_mtime=2024-09-26 09:49:04.337577
2025-08-05 10:27:04,719 - [__init__.py:18] - INFO - ******dataset constants******
2025-08-05 10:27:04,719 - [__init__.py:19] - INFO - DateSize=9000
2025-08-05 10:27:04,719 - [__init__.py:20] - INFO - MinuteSize=283
2025-08-05 10:27:04,719 - [__init__.py:21] - INFO - SymbolSize=6000
2025-08-05 10:27:04,719 - [__init__.py:22] - INFO - DateSize_PreVersion=4897
2025-08-05 10:27:04,719 - [__init__.py:23] - INFO - SymbolSize_PreVersion=5000
